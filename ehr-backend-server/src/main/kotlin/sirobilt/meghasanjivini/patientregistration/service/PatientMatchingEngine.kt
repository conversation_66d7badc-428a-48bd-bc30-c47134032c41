package sirobilt.meghasanjivini.patientregistration.service

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import sirobilt.meghasanjivini.patientregistration.dto.*
import sirobilt.meghasanjivini.patientregistration.model.Patient
import java.time.LocalDate
import java.util.logging.Logger

/**
 * Core engine for patient matching and duplicate detection
 */
@ApplicationScoped
class PatientMatchingEngine {

    @Inject
    lateinit var fuzzyMatchingUtils: FuzzyMatchingUtils

    private val logger: Logger = Logger.getLogger(PatientMatchingEngine::class.java.name)

    /**
     * Calculate match score between a candidate patient and an existing patient
     */
    fun calculateMatchScore(
        candidateData: PatientRegistrationDto,
        existingPatient: Patient,
        weights: MatchingWeights
    ): MatchResult {
        val matchingCriteria = mutableListOf<MatchingCriterion>()
        var totalScore = 0
        var maxPossibleScore = 0

        // Name matching
        val nameScore = calculateNameMatch(candidateData, existingPatient, weights, matchingCriteria)
        totalScore += nameScore
        maxPossibleScore += maxOf(weights.nameExact, weights.nameFuzzy)

        // Date of birth matching
        val dobScore = calculateDateOfBirthMatch(candidateData, existingPatient, weights, matchingCriteria)
        totalScore += dobScore
        maxPossibleScore += weights.dateOfBirth

        // Phone number matching
        val phoneScore = calculatePhoneMatch(candidateData, existingPatient, weights, matchingCriteria)
        totalScore += phoneScore
        maxPossibleScore += weights.phone

        // Email matching
        val emailScore = calculateEmailMatch(candidateData, existingPatient, weights, matchingCriteria)
        totalScore += emailScore
        maxPossibleScore += weights.email

        // Address matching
        val addressScore = calculateAddressMatch(candidateData, existingPatient, weights, matchingCriteria)
        totalScore += addressScore
        maxPossibleScore += weights.address

        // Identifier matching (ABHA, Aadhaar, etc.)
        val identifierScore = calculateIdentifierMatch(candidateData, existingPatient, weights, matchingCriteria)
        totalScore += identifierScore
        maxPossibleScore += weights.identifier

        // For high-confidence scenarios, use raw scores instead of percentages
        // This ensures ABHA matches and exact name+DOB matches get appropriate scores
        val finalScore = when {
            // ABHA exact match should always be high confidence
            matchingCriteria.any { it.field == "abhaNumber" && it.matchType == MatchType.EXACT } -> {
                maxOf(totalScore, 90) // Ensure ABHA matches get at least 90 points
            }
            // Exact name + exact DOB should be high confidence
            matchingCriteria.any { it.field == "fullName" && it.matchType == MatchType.EXACT } &&
            matchingCriteria.any { it.field == "dateOfBirth" && it.matchType == MatchType.EXACT } -> {
                maxOf(totalScore, 85) // Ensure name+DOB matches get at least 85 points
            }
            else -> {
                // For other cases, use percentage scoring but with a more reasonable scale
                if (maxPossibleScore > 0) {
                    (totalScore * 100) / maxPossibleScore
                } else {
                    0
                }
            }
        }

        return MatchResult(
            score = finalScore,
            matchingCriteria = matchingCriteria,
            totalWeightedScore = totalScore,
            maxPossibleScore = maxPossibleScore
        )
    }

    /**
     * Calculate name matching score
     */
    private fun calculateNameMatch(
        candidate: PatientRegistrationDto,
        existing: Patient,
        weights: MatchingWeights,
        criteria: MutableList<MatchingCriterion>
    ): Int {
        val candidateFullName = buildFullName(candidate.firstName, candidate.middleName, candidate.lastName)
        val existingFullName = buildFullName(existing.firstName, existing.middleName, existing.lastName)

        // Exact match check
        if (candidateFullName.equals(existingFullName, ignoreCase = true)) {
            criteria.add(
                MatchingCriterion(
                    field = "fullName",
                    matchType = MatchType.EXACT,
                    score = weights.nameExact,
                    weight = weights.nameExact,
                    originalValue = candidateFullName,
                    matchedValue = existingFullName,
                    similarity = 1.0
                )
            )
            return weights.nameExact
        }

        // Fuzzy match check
        val similarity = fuzzyMatchingUtils.calculateNameSimilarity(
            candidate.firstName, candidate.middleName, candidate.lastName,
            existing.firstName, existing.middleName, existing.lastName
        )

        if (similarity >= 0.8) { // 80% similarity threshold
            val fuzzyScore = (weights.nameFuzzy * similarity).toInt()
            criteria.add(
                MatchingCriterion(
                    field = "fullName",
                    matchType = MatchType.FUZZY,
                    score = fuzzyScore,
                    weight = weights.nameFuzzy,
                    originalValue = candidateFullName,
                    matchedValue = existingFullName,
                    similarity = similarity
                )
            )
            return fuzzyScore
        }

        return 0
    }

    /**
     * Calculate date of birth matching score
     */
    private fun calculateDateOfBirthMatch(
        candidate: PatientRegistrationDto,
        existing: Patient,
        weights: MatchingWeights,
        criteria: MutableList<MatchingCriterion>
    ): Int {
        if (candidate.dateOfBirth == null || existing.dateOfBirth == null) {
            return 0
        }

        if (candidate.dateOfBirth == existing.dateOfBirth) {
            criteria.add(
                MatchingCriterion(
                    field = "dateOfBirth",
                    matchType = MatchType.EXACT,
                    score = weights.dateOfBirth,
                    weight = weights.dateOfBirth,
                    originalValue = candidate.dateOfBirth.toString(),
                    matchedValue = existing.dateOfBirth.toString(),
                    similarity = 1.0
                )
            )
            return weights.dateOfBirth
        }

        return 0
    }

    /**
     * Calculate phone number matching score
     */
    private fun calculatePhoneMatch(
        candidate: PatientRegistrationDto,
        existing: Patient,
        weights: MatchingWeights,
        criteria: MutableList<MatchingCriterion>
    ): Int {
        val candidatePhones = candidate.contacts?.mapNotNull { it.phoneNumber } ?: emptyList()
        val existingPhones = existing.contacts.mapNotNull { it.phoneNumber }

        for (candidatePhone in candidatePhones) {
            for (existingPhone in existingPhones) {
                if (fuzzyMatchingUtils.arePhoneNumbersSimilar(candidatePhone, existingPhone)) {
                    criteria.add(
                        MatchingCriterion(
                            field = "phoneNumber",
                            matchType = MatchType.EXACT,
                            score = weights.phone,
                            weight = weights.phone,
                            originalValue = candidatePhone,
                            matchedValue = existingPhone,
                            similarity = 1.0
                        )
                    )
                    return weights.phone
                }
            }
        }

        return 0
    }

    /**
     * Calculate email matching score
     */
    private fun calculateEmailMatch(
        candidate: PatientRegistrationDto,
        existing: Patient,
        weights: MatchingWeights,
        criteria: MutableList<MatchingCriterion>
    ): Int {
        val candidateEmails = candidate.contacts?.mapNotNull { it.email } ?: emptyList()
        val existingEmails = existing.contacts.mapNotNull { it.email }

        for (candidateEmail in candidateEmails) {
            for (existingEmail in existingEmails) {
                if (fuzzyMatchingUtils.areEmailsSimilar(candidateEmail, existingEmail)) {
                    criteria.add(
                        MatchingCriterion(
                            field = "email",
                            matchType = MatchType.EXACT,
                            score = weights.email,
                            weight = weights.email,
                            originalValue = candidateEmail,
                            matchedValue = existingEmail,
                            similarity = 1.0
                        )
                    )
                    return weights.email
                }
            }
        }

        return 0
    }

    /**
     * Calculate address matching score
     */
    private fun calculateAddressMatch(
        candidate: PatientRegistrationDto,
        existing: Patient,
        weights: MatchingWeights,
        criteria: MutableList<MatchingCriterion>
    ): Int {
        val candidateAddresses = candidate.addresses?.map { buildAddressString(it) } ?: emptyList()
        val existingAddresses = existing.addresses.map { buildAddressString(it) }

        for (candidateAddress in candidateAddresses) {
            for (existingAddress in existingAddresses) {
                if (fuzzyMatchingUtils.areAddressesSimilar(candidateAddress, existingAddress)) {
                    criteria.add(
                        MatchingCriterion(
                            field = "address",
                            matchType = MatchType.FUZZY,
                            score = weights.address,
                            weight = weights.address,
                            originalValue = candidateAddress,
                            matchedValue = existingAddress,
                            similarity = 0.8 // Approximate similarity for address match
                        )
                    )
                    return weights.address
                }
            }
        }

        return 0
    }

    /**
     * Calculate identifier matching score (ABHA, Aadhaar, etc.)
     */
    private fun calculateIdentifierMatch(
        candidate: PatientRegistrationDto,
        existing: Patient,
        weights: MatchingWeights,
        criteria: MutableList<MatchingCriterion>
    ): Int {
        // Check ABHA number
        val candidateAbha = candidate.abha?.abhaNumber
        val existingAbha = existing.abha?.abhaNumber

        if (!candidateAbha.isNullOrBlank() && !existingAbha.isNullOrBlank() && candidateAbha == existingAbha) {
            criteria.add(
                MatchingCriterion(
                    field = "abhaNumber",
                    matchType = MatchType.EXACT,
                    score = weights.identifier,
                    weight = weights.identifier,
                    originalValue = candidateAbha,
                    matchedValue = existingAbha,
                    similarity = 1.0
                )
            )
            return weights.identifier
        }

        // Check main identifier (could be ABHA, Aadhaar, etc.)
        if (!candidate.identifierNumber.isNullOrBlank() && 
            !existing.identifierNumber.isNullOrBlank() && 
            candidate.identifierType == existing.identifierType &&
            candidate.identifierNumber == existing.identifierNumber) {
            
            criteria.add(
                MatchingCriterion(
                    field = "identifierNumber",
                    matchType = MatchType.EXACT,
                    score = weights.identifier,
                    weight = weights.identifier,
                    originalValue = candidate.identifierNumber,
                    matchedValue = existing.identifierNumber,
                    similarity = 1.0
                )
            )
            return weights.identifier
        }

        return 0
    }

    /**
     * Helper function to build full name
     */
    private fun buildFullName(firstName: String?, middleName: String?, lastName: String?): String {
        return listOfNotNull(firstName, middleName, lastName)
            .filter { it.isNotBlank() }
            .joinToString(" ")
    }

    /**
     * Helper function to build address string
     */
    private fun buildAddressString(address: Any): String {
        return when (address) {
            is AddressDto -> {
                listOfNotNull(
                    address.houseNoOrFlatNo,
                    address.localityOrSector,
                    address.cityOrVillage,
                    address.pincode
                ).filter { it.isNotBlank() }.joinToString(", ")
            }
            else -> {
                // Handle PatientAddress entity
                val addressEntity = address as sirobilt.meghasanjivini.patientregistration.model.PatientAddress
                listOfNotNull(
                    addressEntity.houseNoOrFlatNo,
                    addressEntity.localityOrSector,
                    addressEntity.cityOrVillage,
                    addressEntity.pincode
                ).filter { it.isNotBlank() }.joinToString(", ")
            }
        }
    }

    /**
     * Data class for match results
     */
    data class MatchResult(
        val score: Int,
        val matchingCriteria: List<MatchingCriterion>,
        val totalWeightedScore: Int,
        val maxPossibleScore: Int
    )
}
